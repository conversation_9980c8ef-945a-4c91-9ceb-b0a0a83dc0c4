
import request from '@/utils/request'
// 通过ID查询工单
export const getWorkOrder = (id) =>
  request({
    url: '/manage-api/v1/work-order',
    method: 'get',
    params: { id: id }
  })
返回值参考
{
    "errorMessage": "string",
    "code": 1073741824,
    "data": {
      "id": 9007199254740991,
      "communityId": 9007199254740991,
      "type": "string",
      "userDescribe": "string",
      "personDescribe": "string",
      "media": "string",
      "status": "string",
      "createTime": "2025-06-09T11:26:32.268Z",
      "updateTime": "2025-06-09T11:26:32.268Z",
      "residentId": 9007199254740991,
      "personId": 9007199254740991,
      "regionType": "string",
      "region": "string"
    }
  }


// 添加工单
export const addWorkOrder = (data) =>
  request({
    url: '/manage-api/v1/work-order',
    method: 'post',
    data
  })
入参
{
    "communityId": 9007199254740991,
    "type": "string",
    "userDescribe": "string",
    "personDescribe": "string",
    "media": "string",
    "status": "string",
    "residentId": 9007199254740991,
    "personId": 9007199254740991,
    "regionType": "string",
    "region": "string"
  }





// 编辑工单
export const editWorkOrder = (data) =>
  request({
    url: '/manage-api/v1/work-order',
    method: 'put',
    data
  })
  {
    "id": 9007199254740991,
    "communityId": 9007199254740991,
    "type": "string",
    "userDescribe": "string",
    "personDescribe": "string",
    "media": "string",
    "status": "string",
    "residentId": 9007199254740991,
    "personId": 9007199254740991,
    "regionType": "string",
    "region": "string"
  }



// 删除工单
export const deleteWorkOrder = (id) =>
  request({
    url: '/manage-api/v1/work-order',
    method: 'delete',
    params: { id: id }
  })    

  // 工单分页查询
  export const listWorkOrder = (data) =>
  request({
    url: '/manage-api/v1/work-order/page',
    method: 'get',
    params: data
  })
返回值参考
{
    "errorMessage": "string",
    "code": 1073741824,
    "data": {
      "total": 9007199254740991,
      "list": [
        {
          "id": 9007199254740991,
          "communityId": 9007199254740991,
          "type": "string",
          "userDescribe": "string",
          "personDescribe": "string",
          "media": "string",
          "status": "string",
          "createTime": "2025-06-09T11:27:48.796Z",
          "updateTime": "2025-06-09T11:27:48.796Z",
          "residentId": 9007199254740991,
          "personId": 9007199254740991,
          "regionType": "string",
          "region": "string"
        }
      ],
      "pageNum": 1073741824,
      "pageSize": 1073741824,
      "size": 1073741824,
      "startRow": 9007199254740991,
      "endRow": 9007199254740991,
      "pages": 1073741824,
      "prePage": 1073741824,
      "nextPage": 1073741824,
      "isFirstPage": true,
      "isLastPage": true,
      "hasPreviousPage": true,
      "hasNextPage": true,
      "navigatePages": 1073741824,
      "navigatepageNums": [
        1073741824
      ],
      "navigateFirstPage": 1073741824,
      "navigateLastPage": 1073741824
    }
  }

  需要通道d