<template>
  <div class="el-tag-input">
    <!-- 标签显示区域 -->
    <div class="tag-container" @click="focusInput">
      <el-tag
        v-for="(tag, index) in tags"
        :key="index"
        :closable="!disabled"
        @close="removeTag(index)"
        class="tag-item"
        :type="getTagType(tag)"
      >
        {{ getTagLabel(tag) }}
      </el-tag>

      <!-- 输入框 -->
      <el-input
        v-if="!disabled"
        ref="inputRef"
        v-model="inputValue"
        :placeholder="placeholder"
        size="small"
        class="tag-input"
        @keydown.enter.prevent="addTag"
        @keydown.tab.prevent="addTag"
        @keydown.delete="handleDelete"
        @blur="addTag"
        @focus="showSuggestions = true"
      />
    </div>

    <!-- 建议标签列表 -->
    <div v-if="showSuggestions && suggestedTags.length > 0" class="suggestions-container">
      <div class="suggestions-header">
        <span>推荐标签</span>
        <el-button type="text" size="small" @click="showSuggestions = false">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      <div class="suggestions-list">
        <el-tag
          v-for="(tag, index) in filteredSuggestions"
          :key="index"
          :class="{ 'is-selected': isTagSelected(tag) }"
          class="suggestion-tag"
          @click="toggleSuggestedTag(tag)"
          :type="isTagSelected(tag) ? 'success' : getSuggestedTagType(tag)"
        >
          {{ getSuggestedTagLabel(tag) }}
          <el-icon v-if="isTagSelected(tag)" class="check-icon"><Check /></el-icon>
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script>
import { Close, Check } from '@element-plus/icons-vue'

export default {
  name: 'ElTagInput',
  components: {
    Close,
    Check
  },
  props: {
    modelValue: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '请输入标签，按回车或Tab键添加'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    maxTags: {
      type: Number,
      default: 10
    },
    tagType: {
      type: String,
      default: ''
    },
    suggestedTags: {
      type: Array,
      default: () => []
    },
    allowDuplicates: {
      type: Boolean,
      default: false
    },
    // 字典数据，用于标签显示和颜色
    dictData: {
      type: Array,
      default: () => []
    },
    // 是否使用字典模式
    useDictMode: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'change'],
  data() {
    return {
      inputValue: '',
      showSuggestions: false
    }
  },
  computed: {
    tags: {
      get() {
        return this.modelValue || []
      },
      set(value) {
        this.$emit('update:modelValue', value)
        this.$emit('change', value)
      }
    },
    filteredSuggestions() {
      if (!this.inputValue) {
        return this.suggestedTags
      }

      if (this.useDictMode) {
        return this.suggestedTags.filter(tag => {
          const label = this.getSuggestedTagLabel(tag)
          return label.toLowerCase().includes(this.inputValue.toLowerCase())
        })
      } else {
        return this.suggestedTags.filter(tag =>
          tag.toLowerCase().includes(this.inputValue.toLowerCase())
        )
      }
    }
  },
  methods: {
    /**
     * 添加标签
     */
    addTag() {
      const value = this.inputValue.trim()
      if (!value) return

      // 检查是否超过最大数量
      if (this.tags.length >= this.maxTags) {
        this.$message.warning(`最多只能添加${this.maxTags}个标签`)
        return
      }

      // 检查是否重复
      if (!this.allowDuplicates && this.tags.includes(value)) {
        this.$message.warning('标签已存在')
        this.inputValue = ''
        return
      }

      // 添加标签
      this.tags = [...this.tags, value]
      this.inputValue = ''
    },

    /**
     * 移除标签
     */
    removeTag(index) {
      const newTags = [...this.tags]
      newTags.splice(index, 1)
      this.tags = newTags
    },

    /**
     * 处理删除键
     */
    handleDelete() {
      if (this.inputValue === '' && this.tags.length > 0) {
        this.removeTag(this.tags.length - 1)
      }
    },

    /**
     * 聚焦输入框
     */
    focusInput() {
      if (!this.disabled) {
        this.$nextTick(() => {
          this.$refs.inputRef?.focus()
        })
      }
    },

    /**
     * 切换建议标签
     */
    toggleSuggestedTag(tag) {
      const tagValue = this.useDictMode ? this.getTagValue(tag) : tag

      if (this.isTagSelected(tag)) {
        // 移除标签
        const index = this.tags.findIndex(t => {
          return this.useDictMode ? t === tagValue : t === tag
        })
        if (index > -1) {
          this.removeTag(index)
        }
      } else {
        // 添加标签
        if (this.tags.length >= this.maxTags) {
          this.$message.warning(`最多只能添加${this.maxTags}个标签`)
          return
        }
        this.tags = [...this.tags, tagValue]
      }
    },

    /**
     * 获取标签显示文本
     */
    getTagLabel(tag) {
      if (!this.useDictMode) return tag

      const dictItem = this.dictData.find(item => item.nameEn === tag)
      return dictItem ? dictItem.nameCn : tag
    },

    /**
     * 获取标签类型（颜色）
     */
    getTagType(tag) {
      if (!this.useDictMode) return this.tagType

      const dictItem = this.dictData.find(item => item.nameEn === tag)
      return dictItem?.tagType || this.tagType || this.getDefaultTagType(tag)
    },

    /**
     * 获取建议标签显示文本
     */
    getSuggestedTagLabel(tag) {
      if (!this.useDictMode) return tag

      if (typeof tag === 'object') {
        return tag.nameCn || tag.label || tag.nameEn || tag.value
      }

      const dictItem = this.dictData.find(item => item.nameEn === tag)
      return dictItem ? dictItem.nameCn : tag
    },

    /**
     * 获取建议标签类型
     */
    getSuggestedTagType(tag) {
      if (!this.useDictMode) return ''

      if (typeof tag === 'object') {
        return tag.tagType || ''
      }

      const dictItem = this.dictData.find(item => item.nameEn === tag)
      return dictItem?.tagType || this.getDefaultTagType(tag)
    },

    /**
     * 获取标签值（用于存储）
     */
    getTagValue(tag) {
      if (!this.useDictMode) return tag

      if (typeof tag === 'object') {
        return tag.nameEn || tag.value
      }

      return tag
    },

    /**
     * 检查标签是否已选中
     */
    isTagSelected(tag) {
      const tagValue = this.useDictMode ? this.getTagValue(tag) : tag
      return this.tags.includes(tagValue)
    },

    /**
     * 获取默认标签类型
     */
    getDefaultTagType(tag) {
      // 根据标签内容返回不同颜色
      const colorMap = {
        '老人': 'warning',
        '低保': 'danger',
        '党员': 'danger',
        '军人': 'success',
        '残疾人': 'warning',
        'VIP': 'danger',
        '业主': 'success',
        '租户': 'primary',
        '独居老人': 'warning',
        '空巢老人': 'warning',
        '留守儿童': 'warning',
        '困难户': 'danger',
        '五保户': 'danger',
        '退休干部': 'info',
        '教师': 'success',
        '医生': 'success',
        '志愿者': 'primary',
        '网格员': 'primary'
      }

      const label = this.getTagLabel ? this.getTagLabel(tag) : tag
      return colorMap[label] || ''
    }
  },
  mounted() {
    // 点击外部关闭建议列表
    document.addEventListener('click', (e) => {
      if (!this.$el.contains(e.target)) {
        this.showSuggestions = false
      }
    })
  }
}
</script>

<style scoped>
.el-tag-input {
  position: relative;
  width: 100%;
}

.tag-container {
  min-height: 32px;
  padding: 4px 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4px;
  cursor: text;
  transition: border-color 0.2s;
}

.tag-container:hover {
  border-color: #c0c4cc;
}

.tag-container:focus-within {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.tag-item {
  margin: 0;
  flex-shrink: 0;
}

.tag-input {
  flex: 1;
  min-width: 120px;
  border: none;
  outline: none;
}

.tag-input :deep(.el-input__wrapper) {
  box-shadow: none;
  padding: 0;
}

.suggestions-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 4px;
  max-height: 200px;
  overflow-y: auto;
}

.suggestions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;
  color: #909399;
  background-color: #fafafa;
}

.suggestions-list {
  padding: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.suggestion-tag {
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  padding-right: 20px;
}

.suggestion-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.suggestion-tag.is-selected {
  background-color: #f0f9ff;
  border-color: #409eff;
  color: #409eff;
}

.check-icon {
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
}

/* 禁用状态 */
.el-tag-input.is-disabled .tag-container {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  cursor: not-allowed;
}

.el-tag-input.is-disabled .tag-item {
  background-color: #f0f2f5;
}
</style>
