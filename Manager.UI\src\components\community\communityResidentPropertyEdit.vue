<template>
  <el-dialog
    :title="dialog.title"
    v-model="dialog.show"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form
      ref="form"
      :model="propertyModel"
      :rules="rules"
      label-width="100px"
      class="property-edit-form"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="住户信息">
            <el-input
              :value="`${residentInfo.residentName} (${residentInfo.phone})`"
              disabled
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="楼栋" prop="buildingId">
            <el-select
              v-model="propertyModel.buildingId"
              placeholder="请选择楼栋"
              filterable
              clearable
              style="width: 100%"
              :loading="buildingLoading"
              @change="onBuildingChange"
            >
              <el-option
                v-for="building in buildingList"
                :key="building.id"
                :label="building.buildingNumber"
                :value="building.id"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="房间" prop="roomId">
            <el-select
              v-model="propertyModel.roomId"
              placeholder="请选择房间"
              filterable
              clearable
              style="width: 100%"
              :loading="roomLoading"
              @change="onRoomChange"
            >
              <el-option
                v-for="room in roomList"
                :key="room.id"
                :label="`${room.unitNumber}${room.roomNumber}`"
                :value="room.id"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="完整地址">
            <el-input
              v-model="propertyModel.address"
              placeholder="选择房间后自动生成"
              disabled
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="住户类型" prop="residentType">
            <el-select
              v-model="propertyModel.residentType"
              placeholder="请选择住户类型"
              style="width: 100%"
            >
              <el-option
                v-for="type in RESIDENT_TYPES"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="关系类型" prop="relationshipType">
            <el-select
              v-model="propertyModel.relationshipType"
              placeholder="请选择关系类型"
              style="width: 100%"
            >
              <el-option
                v-for="type in RELATIONSHIP_TYPES"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="开始日期" prop="startDate">
            <el-date-picker
              v-model="propertyModel.startDate"
              type="date"
              placeholder="请选择开始日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="结束日期" prop="endDate">
            <el-date-picker
              v-model="propertyModel.endDate"
              type="date"
              placeholder="租户必填，业主可不填"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled="propertyModel.residentType === 'owner'"
            />
            <div v-if="propertyModel.residentType === 'owner'" style="font-size: 12px; color: #999; margin-top: 5px">
              业主房产无需填写结束日期
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="propertyModel.status"
              placeholder="请选择状态"
              style="width: 100%"
            >
              <el-option
                v-for="status in PROPERTY_STATUS"
                :key="status.value"
                :label="status.label"
                :value="status.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="propertyModel.note"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button type="primary" @click="submit" :loading="submitting">
          保存
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  addResidentProperty,
  editResidentProperty,
  RELATIONSHIP_TYPES,
  PROPERTY_STATUS
} from '@/api/community/communityResidentProperty'
import { RESIDENT_TYPES } from '@/api/community/communityResident'
import { listCommunityBuilding } from '@/api/community/communityBuilding'
import { listCommunityRoom } from '@/api/community/communityRoom'
import { getSelectedCommunityId } from '@/store/modules/options'

export default {
  name: 'CommunityResidentPropertyEdit',
  emits: ['refresh'],
  data() {
    return {
      dialog: {
        show: false,
        title: ''
      },
      residentInfo: {
        residentId: null,
        residentName: '',
        phone: ''
      },
      propertyModel: {
        id: null,
        residentId: null,
        communityId: null,
        buildingId: null,
        roomId: null,
        address: '',
        residentType: 'owner',
        relationshipType: 'self',
        startDate: '',
        endDate: '',
        status: 'normal',
        note: ''
      },
      rules: {
        buildingId: [
          { required: true, message: '请选择楼栋', trigger: 'change' }
        ],
        roomId: [
          { required: true, message: '请选择房间', trigger: 'change' }
        ],
        residentType: [
          { required: true, message: '请选择住户类型', trigger: 'change' }
        ],
        relationshipType: [
          { required: true, message: '请选择关系类型', trigger: 'change' }
        ],
        startDate: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      },
      buildingList: [],
      buildingLoading: false,
      roomList: [],
      roomLoading: false,
      submitting: false,
      RESIDENT_TYPES,
      RELATIONSHIP_TYPES,
      PROPERTY_STATUS
    }
  },

  methods: {
    /**
     * 打开对话框
     */
    open(residentInfo, property = null) {
      this.residentInfo = { ...residentInfo }

      if (property) {
        debugger
        this.propertyModel = { ...property }
        this.dialog.title = '编辑房产信息'
        // 编辑时需要加载对应的房间列表
        if (property.buildingId) {
          this.loadRoomList(property.buildingId)
        }
      } else {
        this.resetForm()
        debugger
        this.propertyModel.residentId = residentInfo.id

        this.propertyModel.communityId = getSelectedCommunityId()
        this.dialog.title = '新增房产信息'
      }

      this.dialog.show = true
      this.loadBuildingList()
    },

    /**
     * 加载楼栋列表
     */
    loadBuildingList() {
      this.buildingLoading = true
      const params = {
        pageNum: 1,
        pageSize: 500,
        communityId: getSelectedCommunityId()
      }

      listCommunityBuilding(params)
        .then(res => {
          this.buildingList = res.data.data.list || []
        })
        .catch(err => {
          console.error('加载楼栋列表失败:', err)
          this.$message.error('加载楼栋列表失败')
          this.buildingList = []
        })
        .finally(() => {
          this.buildingLoading = false
        })
    },

    /**
     * 加载房间列表
     */
    loadRoomList(buildingId) {
      if (!buildingId) {
        this.roomList = []
        return
      }

      this.roomLoading = true
      const params = {
        pageNum: 1,
        pageSize: 500,
        buildingId: buildingId,
        communityId: getSelectedCommunityId()
      }

      listCommunityRoom(params)
        .then(res => {
          this.roomList = res.data.data.list || []
        })
        .catch(err => {
          console.error('加载房间列表失败:', err)
          this.$message.error('加载房间列表失败')
          this.roomList = []
        })
        .finally(() => {
          this.roomLoading = false
        })
    },

    /**
     * 楼栋变化处理
     */
    onBuildingChange(buildingId) {
      this.propertyModel.roomId = null
      this.propertyModel.address = ''
      this.roomList = []

      if (buildingId) {
        this.loadRoomList(buildingId)
      }
    },

    /**
     * 房间变化处理
     */
    onRoomChange(roomId) {
      if (roomId) {
        const selectedBuilding = this.buildingList.find(b => b.id === this.propertyModel.buildingId)
        const selectedRoom = this.roomList.find(r => r.id === roomId)

        if (selectedBuilding && selectedRoom) {
          this.propertyModel.address = `${selectedBuilding.buildingNumber}${selectedRoom.unitNumber}${selectedRoom.roomNumber}`
        }
      } else {
        this.propertyModel.address = ''
      }
    },

    /**
     * 提交表单
     */
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return

        // 租户必须填写结束日期
        if (this.propertyModel.residentType === 'tenant' && !this.propertyModel.endDate) {
          this.$message.warning('租户必须填写结束日期')
          return
        }

        this.submitting = true
        const api = this.propertyModel.id ? editResidentProperty : addResidentProperty

        api(this.propertyModel)
          .then(() => {
            this.$message.success('保存成功')
            this.dialog.show = false
            this.$emit('refresh')
          })
          .catch(err => {
            this.$message.error(err.data?.errorMessage || '保存失败')
          })
          .finally(() => {
            this.submitting = false
          })
      })
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.$refs.form && this.$refs.form.resetFields()
      this.propertyModel = {
        id: null,
        residentId: null,
        communityId: null,
        buildingId: null,
        roomId: null,
        address: '',
        residentType: 'owner',
        relationshipType: 'self',
        startDate: '',
        endDate: '',
        status: 'normal',
        note: ''
      }
      this.roomList = []
    }
  }
}
</script>

<style scoped>
.property-edit-form {
  padding: 0 10px;
}

.dialog-footer {
  text-align: right;
}
</style>
