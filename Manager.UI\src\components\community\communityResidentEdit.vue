<template>
  <el-dialog
    :title="dialog.title"
    v-model="dialog.show"
    class="resident-edit-dialog"
  >
    <div style="padding-top: 20px">
      <el-form label-width="120px" ref="form" :model="residentModel">
        <el-row :span="24">
          <el-col :span="12">
            <el-form-item label="住户姓名" prop="userName">
              <el-input
                v-model="residentModel.residentName"
                :min="0"
                style="width: 100%"
                placeholder="请输入住户姓名"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phone">
              <el-input
                v-model="residentModel.phone"
                :min="0"
                style="width: 100%"
                placeholder="请输入手机号码"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="12">
            <el-form-item label="证件类型" prop="certificateType">
              <el-select
                v-model="residentModel.certificateType"
                placeholder="请选择证件类型"
                style="width: 100%"
              >
                <el-option label="身份证" value="id_card" />
                <el-option label="护照" value="passport" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="身份证号" prop="idCardNumber">
              <el-input
                v-model="residentModel.idCardNumber"
                :min="0"
                style="width: 100%"
                placeholder="请输入身份证号"
              />
            </el-form-item>
          </el-col>
        </el-row>


        <el-col :span="24">
          <el-form-item label="标签" prop="tags">
            <el-tag-input
              v-model="residentTagsArray"
              placeholder="请输入标签，按回车或Tab键添加"
              :suggested-tags="suggestedTags"
              :dict-data="dictData"
              :use-dict-mode="true"
              :max-tags="10"
              @change="onTagsChange"
            />
            <div style="font-size: 12px; color: #999; margin-top: 5px">
              可以手动输入标签，也可以从推荐标签中选择
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="备注" prop="note">
            <el-input
              v-model="residentModel.note"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-form>


    </div>
    <div class="dialog-footer">
      <el-button @click="dialog.show = false">取消</el-button>
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  addCommunityResident,
  editCommunityResident,
} from "@/api/community/communityResident";
import { eventManagerMixin } from "@/utils/eventManager";
import { listCommunityBuilding } from "@/api/community/communityBuilding";
import { getSelectedCommunityId } from "@/store/modules/options";
import { listCommunityRoom } from "@/api/community/communityRoom";
import { listDictByNameEn } from "@/api/system/dict";
import ElTagInput from "@/components/common/ElTagInput.vue";

export default {
  name: "communityResidentEdit",
  components: {
    ElTagInput
  },
  mixins: [eventManagerMixin],
  data() {
    return {
      residentModel: {
        roomId: "",
        buildingNumber: "",
        unitNumber: "",
        residentName: "",
        certificateType: "",
        idCardNumber: "",
        residentType: "",
        status: "",
        note: "",
        phone: "",
        tags: "",
        communityId: null,
      },
      dialog: {
        show: false,
        title: "",
      },
      uploadUrl: import.meta.env.VITE_BASE_API + "/common-api/v1/file/upload",
      uploadHeaders: {
        Authorization: "",
      },
      rules: {
        buildingId: [
          { required: true, message: "请输入楼房ID", trigger: "blur" },
        ],
        roomId: [
          { required: true, message: "请输入 residualID", trigger: "blur" },
        ],
        residentId: [
          { required: true, message: "请输入住户ID", trigger: "blur" },
        ],
        residentType: [
          { required: true, message: "请选择住户类型", trigger: "change" },
        ],
        status: [{ required: true, message: "请选择状态", trigger: "change" }],
        phone: [
          {
            pattern: /^1[3-9]\d{9}$/,
            message: "请输入正确的手机号",
            trigger: "blur",
          },
        ],
      },
      buildingList: [],
      buildingLoading: false,
      searchModel: {
        pageNum: 1,
        pageSize: 500,
        buildingNumber: "",
        buildingId: "",
        type: "",
        communityId: null,
      },

      roomList: [],
      roomLoading: false,

      // 标签相关数据
      residentTagsArray: [],
      suggestedTags: [],
      dictData: [],
    };
  },

  computed: {
    // 这里可以添加其他计算属性
  },

  watch: {
    // 这里可以添加其他监听器
  },

  created() {
    this.search();
    this.initDictData();
  },

  methods: {
    /**
     * 标签变化处理
     */
    onTagsChange(tags) {
      this.residentTagsArray = tags
      // 保存nameEn值，以逗号分隔
      this.residentModel.tags = tags.join(',')
    },

    /**
     * 初始化字典数据
     */
    async initDictData() {
      try {
        const res = await listDictByNameEn('resident_tag')
        this.dictData = res.data.data || []
        this.suggestedTags = this.dictData.map(item => ({
          nameEn: item.nameEn,
          nameCn: item.nameCn,
          tagType: this.getTagTypeByName(item.nameCn)
        }))
      } catch (err) {
        console.error('加载住户标签字典失败:', err)
        // 如果字典加载失败，使用默认标签
        this.suggestedTags = [
          { nameEn: 'elderly', nameCn: '老人', tagType: 'warning' },
          { nameEn: 'low_income', nameCn: '低保', tagType: 'danger' },
          { nameEn: 'party_member', nameCn: '党员', tagType: 'danger' },
          { nameEn: 'military', nameCn: '军人', tagType: 'success' },
          { nameEn: 'disabled', nameCn: '残疾人', tagType: 'warning' },
          { nameEn: 'vip', nameCn: 'VIP', tagType: 'danger' },
          { nameEn: 'owner', nameCn: '业主', tagType: 'success' },
          { nameEn: 'tenant', nameCn: '租户', tagType: 'primary' }
        ]
        this.dictData = this.suggestedTags
      }
    },

    /**
     * 根据标签名称获取标签类型
     */
    getTagTypeByName(name) {
      const colorMap = {
        '老人': 'warning',
        '低保': 'danger',
        '党员': 'danger',
        '军人': 'success',
        '残疾人': 'warning',
        'VIP': 'danger',
        '业主': 'success',
        '租户': 'primary',
        '独居老人': 'warning',
        '空巢老人': 'warning',
        '留守儿童': 'warning',
        '困难户': 'danger',
        '五保户': 'danger',
        '退休干部': 'info',
        '教师': 'success',
        '医生': 'success',
        '志愿者': 'primary',
        '网格员': 'primary'
      }
      return colorMap[name] || ''
    },



    /**
     * 楼栋选择变化处理
     */
    onBuildingChange(buildingId) {
      const selectBuilding = this.buildingList.find(
        (item) => item.id === buildingId
      );
      debugger;
      if (buildingId) {
        debugger;
        this.roomLoading = true;
        this.residentModel.buildingNumber = this.searchModel.buildingNumber =
          selectBuilding.buildingNumber;
        this.residentModel.buildingId = this.searchModel.buildingId =
          buildingId;

        listCommunityRoom(this.searchModel)
          .then((res) => {
            this.roomLoading = false;
            this.roomList = res.data.data.list || [];
          })
          .catch((err) => {
            this.roomLoading = false;
            console.error("加载房间列表失败:", err);
            this.roomList = [];
          });
      }
    },
    onRoomChange(roomId) {
      // 房间选择变化处理，可以在这里添加其他逻辑
      console.log('选择房间ID:', roomId);
    },

    search() {
      debugger;
      this.buildingLoading = true;
      // 自动使用全局选中的小区ID
      const selectedCommunityId = getSelectedCommunityId();
      const searchParams = {
        ...this.searchModel,
        communityId: selectedCommunityId,
      };

      listCommunityBuilding(searchParams)
        .then((res) => {
          this.buildingLoading = false;
          this.buildingList = res.data.data.list;
          this.total = res.data.data.total;
        })
        .catch((err) => {
          this.buildingLoading = false;
          console.error("查询楼栋列表失败:", err);
          this.$message.error(err.data?.errorMessage || "查询失败");
        });
    },

    resetForm() {
      this.$refs.form && this.$refs.form.resetFields();
      this.residentModel = {
        id: undefined,
        roomId: "",
        buildingNumber: "",
        unitNumber: "",
        residentName: "",
        certificateType: "",
        idCardNumber: "",
        residentType: "",
        status: "normal",
        note: "",
        phone: "",
        tags: "",
        communityId: null,
      };
      // 重置标签数组
      this.residentTagsArray = [];
    },
    submit() {
      // 验证表单
      this.$refs.form.validate((valid) => {
        if (!valid) return;

        // 设置小区ID
        const selectedCommunityId = getSelectedCommunityId();
        this.residentModel.communityId = selectedCommunityId;

        // 调用新增或编辑接口
        const api = this.residentModel.id ? editCommunityResident : addCommunityResident;

        api(this.residentModel)
          .then(() => {
            this.$message.success("保存成功");
            this.dialog.show = false;
            // 触发列表刷新
            this.$emit("search");
            // 通过事件总线通知列表刷新
            this.$safeEmit('refreshResidentList');
          })
          .catch((err) => {
            this.$message.error(err.data?.errorMessage || "保存失败");
          });
      });
    },
    handlePhotoSuccess(response) {
      if (response.code === 0) {
        this.residentModel.photo = response.data;
        this.$message.success("照片上传成功");
      } else {
        this.$message.error(response.errorMessage || "照片上传失败");
      }
    },
    beforePhotoUpload(file) {
      const isImage = file.type.startsWith("image/");
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isImage) {
        this.$message.error("只能上传图片文件!");
        return false;
      }
      if (!isLt5M) {
        this.$message.error("上传图片大小不能超过 5MB!");
        return false;
      }
      return true;
    },
    getImageUrl(photo) {
      if (!photo) return "";
      // 如果是相对路径，需要拼接服务器地址
      if (photo.startsWith("/")) {
        return import.meta.env.VITE_SERVER_IP + photo;
      }
      return photo;
    },
    updateUploadHeaders() {
      const token = localStorage.getItem("token");
      if (token) {
        this.uploadHeaders.Authorization = `Bearer ${token}`;
      }
    },
  },
  mounted() {
    this.updateUploadHeaders();

    // 使用安全的事件监听器注册
    this.$safeOn("openCommunityResidentEdit", (data) => {
      console.log('打开住户编辑对话框');
      this.resetForm();
      if (data && data.id) {
        this.residentModel = { ...data };
        // 处理标签数据：将nameEn字符串转换为数组
        if (data.tags) {
          this.residentTagsArray = data.tags.split(',').filter(tag => tag.trim());
        } else {
          this.residentTagsArray = [];
        }
        this.dialog.title = "编辑住户信息";
      } else {
        this.dialog.title = "新增住户信息";
        this.residentTagsArray = [];
      }
      this.dialog.show = true;
    });
  },

  beforeUnmount() {
    // 组件销毁时的清理工作
  },
};
</script>

<style scoped>
.resident-edit-dialog >>> .el-dialog__body {
  padding-top: 10px;
  padding-bottom: 0;
  position: relative;
  border: 1px solid red;
  width: 700px;
  height: 700px;
}
.resident-edit-form {
  padding: 0 10px;
}
.dialog-footer {
  padding: 10px 24px 18px 0;
  text-align: right;
}

.step-content {
  width: 200px;
  display: flex;
  top: 10px;
  left: 200px;
  align-items: center;
  padding-left: 50px;
  position: absolute;
}

.step-left .el-step__title {
  padding-left: -50px; /* 调整这个值来移动文字 */
}
</style>