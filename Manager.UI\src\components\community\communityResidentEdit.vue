<template>
  <el-dialog
    :title="dialog.title"
    v-model="dialog.show"
    class="resident-edit-dialog"
  >
    <div style="padding-top: 20px">
      <el-form label-width="120px" ref="form" :model="residentModel">
        <el-row :span="24">
          <el-col :span="12">
            <el-form-item label="住户姓名" prop="userName">
              <el-input
                v-model="residentModel.residentName"
                :min="0"
                style="width: 100%"
                placeholder="请输入住户姓名"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phone">
              <el-input
                v-model="residentModel.phone"
                :min="0"
                style="width: 100%"
                placeholder="请输入手机号码"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="12">
            <el-form-item label="证件类型" prop="certificateType">
              <el-select
                v-model="residentModel.certificateType"
                placeholder="请选择证件类型"
                style="width: 100%"
              >
                <el-option label="身份证" value="id_card" />
                <el-option label="护照" value="passport" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="身份证号" prop="idCardNumber">
              <el-input
                v-model="residentModel.idCardNumber"
                :min="0"
                style="width: 100%"
                placeholder="请输入身份证号"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 验证状态提示 -->
        <el-col :span="24" v-if="verificationMessage">
          <el-alert
            :title="verificationMessage"
            :type="verificationStatus === 'verified' ? 'success' :
                   verificationStatus === 'verifying' ? 'info' :
                   verificationStatus === 'failed' ? 'error' : 'warning'"
            :closable="false"
            show-icon
            style="margin-bottom: 20px"
          />
        </el-col>

        <el-col :span="24">
          <el-form-item label="住址" prop="address">
            <el-input
              v-model="residentModel.address"
              placeholder="请输入住址"
              clearable
              :disabled="!isSecondaryFieldsEditable"
            />
            <div v-if="!isSecondaryFieldsEditable" style="font-size: 12px; color: #999; margin-top: 5px">
              请先完成用户信息验证后再填写此项
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="标签" prop="tags">
            <el-tag-input
              v-model="residentTagsArray"
              placeholder="请输入标签，按回车或Tab键添加"
              :disabled="!isSecondaryFieldsEditable"
              :suggested-tags="suggestedTags"
              :max-tags="10"
              @change="onTagsChange"
            />
            <div v-if="isSecondaryFieldsEditable" style="font-size: 12px; color: #999; margin-top: 5px">
              可以手动输入标签，也可以从推荐标签中选择
            </div>
            <div v-else style="font-size: 12px; color: #999; margin-top: 5px">
              请先完成用户信息验证后再填写此项
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="备注" prop="note">
            <el-input
              v-model="residentModel.note"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
              :disabled="!isSecondaryFieldsEditable"
            />
            <div v-if="!isSecondaryFieldsEditable" style="font-size: 12px; color: #999; margin-top: 5px">
              请先完成用户信息验证后再填写此项
            </div>
          </el-form-item>
        </el-col>
      </el-form>


    </div>
    <div class="dialog-footer">
      <el-button @click="dialog.show = false">取消</el-button>
      <el-button
        type="primary"
        @click="submit"
        :disabled="!isVerified"
        :loading="isVerifying"
      >
        {{ isVerifying ? '验证中...' : '保存' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  addCommunityResident,
  editCommunityResident,
  residentVerify,
} from "@/api/community/communityResident";
import { eventManagerMixin } from "@/utils/eventManager";
import { listCommunityBuilding } from "@/api/community/communityBuilding";
import { getSelectedCommunityId } from "@/store/modules/options";
import { listCommunityRoom } from "@/api/community/communityRoom";
import ElTagInput from "@/components/common/ElTagInput.vue";

export default {
  name: "communityResidentEdit",
  components: {
    ElTagInput
  },
  mixins: [eventManagerMixin],
  data() {
    return {
      residentModel: {
        roomId: "",
        buildingNumber: "",
        unitNumber: "",
        residentName: "",
        certificateType: "",
        idCardNumber: "",
        residentType: "",
        status: "",
        note: "",
        phone: "",
        address: "",
        tags: "",
        communityId: null,
      },
      dialog: {
        show: false,
        title: "",
      },
      // 验证状态管理
      verificationStatus: 'pending', // pending, verifying, verified, failed
      verificationMessage: '',
      // 上次验证的数据哈希，用于检测数据变化
      lastVerifiedDataHash: '',
      uploadUrl: import.meta.env.VITE_BASE_API + "/common-api/v1/file/upload",
      uploadHeaders: {
        Authorization: "",
      },
      rules: {
        buildingId: [
          { required: true, message: "请输入楼房ID", trigger: "blur" },
        ],
        roomId: [
          { required: true, message: "请输入 residualID", trigger: "blur" },
        ],
        residentId: [
          { required: true, message: "请输入住户ID", trigger: "blur" },
        ],
        residentType: [
          { required: true, message: "请选择住户类型", trigger: "change" },
        ],
        status: [{ required: true, message: "请选择状态", trigger: "change" }],
        phone: [
          {
            pattern: /^1[3-9]\d{9}$/,
            message: "请输入正确的手机号",
            trigger: "blur",
          },
        ],
      },
      buildingList: [],
      buildingLoading: false,
      searchModel: {
        pageNum: 1,
        pageSize: 500,
        buildingNumber: "",
        buildingId: "",
        type: "",
        communityId: null,
      },

      roomList: [],
      roomLoading: false,

      // 标签相关数据
      residentTagsArray: [],
      suggestedTags: [
        '老人', '低保', '党员', '军人', '残疾人',
        'VIP', '长期租户', '业主', '租户', '临时住户',
        '独居老人', '空巢老人', '留守儿童', '困难户', '五保户',
        '退休干部', '教师', '医生', '志愿者', '网格员'
      ],
    };
  },

  computed: {
    // 检查手机号是否完整（11位数字）
    isPhoneComplete() {
      return /^1[3-9]\d{9}$/.test(this.residentModel.phone);
    },

    // 检查身份证号是否完整（18位）
    isIdCardComplete() {
      return /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/.test(this.residentModel.idCardNumber);
    },

    // 检查关键字段是否已填写完整
    isBasicInfoComplete() {
      return !!(
        this.residentModel.residentName &&
        this.residentModel.residentName.trim() &&
        this.isPhoneComplete &&
        this.residentModel.certificateType &&
        this.isIdCardComplete
      );
    },

    // 检查是否已验证通过
    isVerified() {
      return this.verificationStatus === 'verified';
    },

    // 检查是否正在验证
    isVerifying() {
      return this.verificationStatus === 'verifying';
    },

    // 地址、标签、备注字段是否可编辑
    isSecondaryFieldsEditable() {
      return this.isVerified;
    },

    // 生成验证数据的唯一标识，用于检测数据变化
    verificationDataHash() {
      if (!this.isBasicInfoComplete) return '';
      return `${this.residentModel.residentName.trim()}_${this.residentModel.phone}_${this.residentModel.certificateType}_${this.residentModel.idCardNumber}`;
    }
  },

  watch: {
    // 监听关键字段变化，自动触发验证
    'residentModel.residentName': 'handleFieldChange',
    'residentModel.phone': 'handleFieldChange',
    'residentModel.certificateType': 'handleFieldChange',
    'residentModel.idCardNumber': 'handleFieldChange',

    // 监听验证数据哈希变化，支持重复验证
    verificationDataHash: {
      handler: 'checkAutoVerify',
      immediate: false
    }
  },

  created() {
    this.search();
  },

  methods: {
    /**
     * 标签变化处理
     */
    onTagsChange(tags) {
      this.residentTagsArray = tags
      this.residentModel.tags = tags.join(',')
    },

    /**
     * 处理字段变化
     */
    handleFieldChange() {
      // 清除之前的延迟验证定时器
      if (this.verifyTimer) {
        clearTimeout(this.verifyTimer);
      }

      // 如果基本信息不完整，重置验证状态
      if (!this.isBasicInfoComplete) {
        this.resetVerificationStatus();
        return;
      }

      // 延迟500ms后检查是否需要验证，避免用户输入过程中频繁验证
      this.verifyTimer = setTimeout(() => {
        this.checkAutoVerify();
      }, 500);
    },

    /**
     * 检查是否需要自动验证
     */
    checkAutoVerify() {
      // 如果基本信息不完整，不进行验证
      if (!this.isBasicInfoComplete) {
        return;
      }

      const currentDataHash = this.verificationDataHash;

      // 如果数据没有变化且已经验证过，不重复验证
      if (currentDataHash === this.lastVerifiedDataHash && this.verificationStatus === 'verified') {
        return;
      }

      // 如果正在验证中，不重复验证
      if (this.verificationStatus === 'verifying') {
        return;
      }

      console.log('触发自动验证，数据哈希:', currentDataHash);
      this.performVerification();
    },

    /**
     * 执行用户验证
     */
    performVerification() {
      this.verificationStatus = 'verifying';
      this.verificationMessage = '正在验证用户信息...';

      const selectedCommunityId = getSelectedCommunityId();
      const verifyData = {
        residentName: this.residentModel.residentName.trim(),
        certificateType: this.residentModel.certificateType,
        idCardNumber: this.residentModel.idCardNumber,
        communityId: selectedCommunityId,
        phone: this.residentModel.phone
      };

      // 记录当前验证的数据哈希
      const currentDataHash = this.verificationDataHash;

      residentVerify(verifyData)
        .then(() => {
          this.verificationStatus = 'verified';
          this.verificationMessage = '用户验证成功';
          this.lastVerifiedDataHash = currentDataHash; // 记录成功验证的数据哈希
          this.$message.success("用户验证成功，现在可以填写其他信息");
        })
        .catch((err) => {
          this.verificationStatus = 'failed';
          this.verificationMessage = err.data?.errorMessage || '验证失败，请检查输入信息';
          this.lastVerifiedDataHash = ''; // 清空验证哈希
          this.$message.error(this.verificationMessage);
        });
    },

    /**
     * 重置验证状态
     */
    resetVerificationStatus() {
      this.verificationStatus = 'pending';
      this.verificationMessage = '';
      this.lastVerifiedDataHash = '';

      // 清除验证定时器
      if (this.verifyTimer) {
        clearTimeout(this.verifyTimer);
        this.verifyTimer = null;
      }
    },

    /**
     * 楼栋选择变化处理
     */
    onBuildingChange(buildingId) {
      const selectBuilding = this.buildingList.find(
        (item) => item.id === buildingId
      );
      debugger;
      if (buildingId) {
        debugger;
        this.roomLoading = true;
        this.residentModel.buildingNumber = this.searchModel.buildingNumber =
          selectBuilding.buildingNumber;
        this.residentModel.buildingId = this.searchModel.buildingId =
          buildingId;

        listCommunityRoom(this.searchModel)
          .then((res) => {
            this.roomLoading = false;
            this.roomList = res.data.data.list || [];
          })
          .catch((err) => {
            this.roomLoading = false;
            console.error("加载房间列表失败:", err);
            this.roomList = [];
          });
      }
    },
    onRoomChange(roomId) {
      const selectRoom = this.roomList.find((item) => item.id === roomId);
      debugger;
      this.residentModel.address =
        this.residentModel.buildingNumber +
        selectRoom.unitNumber +
        selectRoom.roomNumber;
    },

    search() {
      debugger;
      this.buildingLoading = true;
      // 自动使用全局选中的小区ID
      const selectedCommunityId = getSelectedCommunityId();
      const searchParams = {
        ...this.searchModel,
        communityId: selectedCommunityId,
      };

      listCommunityBuilding(searchParams)
        .then((res) => {
          this.buildingLoading = false;
          this.buildingList = res.data.data.list;
          this.total = res.data.data.total;
        })
        .catch((err) => {
          this.buildingLoading = false;
          console.error("查询楼栋列表失败:", err);
          this.$message.error(err.data?.errorMessage || "查询失败");
        });
    },

    resetForm() {
      this.$refs.form && this.$refs.form.resetFields();
      this.residentModel = {
        id: undefined,
        roomId: "",
        buildingNumber: "",
        unitNumber: "",
        residentName: "",
        certificateType: "",
        idCardNumber: "",
        residentType: "",
        status: "normal",
        note: "",
        phone: "",
        address: "",
        tags: "",
        communityId: null,
      };
      // 重置标签数组
      this.residentTagsArray = [];
      // 重置验证状态
      this.resetVerificationStatus();
    },
    submit() {
      var that=this
      // 检查是否已验证
      if (!this.isVerified) {
        this.$message.warning("请先完成用户信息验证");
        return;
      }

      // 验证表单
      this.$refs.form.validate((valid) => {
        if (!valid) return;

        // 设置小区ID
        const selectedCommunityId = getSelectedCommunityId();
        that.residentModel.communityId = selectedCommunityId;

        // 调用新增或编辑接口
        const api = that.residentModel.id ? editCommunityResident : addCommunityResident;

        api(that.residentModel)
          .then(() => {
            that.dialog.show = false;
            this.$message.success("保存成功");

            this.$emit("search");
          })
          .catch((err) => {
            this.$message.error(err.data?.errorMessage || "保存失败");
          });
      });
    },
    handlePhotoSuccess(response) {
      if (response.code === 0) {
        this.residentModel.photo = response.data;
        this.$message.success("照片上传成功");
      } else {
        this.$message.error(response.errorMessage || "照片上传失败");
      }
    },
    beforePhotoUpload(file) {
      const isImage = file.type.startsWith("image/");
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isImage) {
        this.$message.error("只能上传图片文件!");
        return false;
      }
      if (!isLt5M) {
        this.$message.error("上传图片大小不能超过 5MB!");
        return false;
      }
      return true;
    },
    getImageUrl(photo) {
      if (!photo) return "";
      // 如果是相对路径，需要拼接服务器地址
      if (photo.startsWith("/")) {
        return import.meta.env.VITE_SERVER_IP + photo;
      }
      return photo;
    },
    updateUploadHeaders() {
      const token = localStorage.getItem("token");
      if (token) {
        this.uploadHeaders.Authorization = `Bearer ${token}`;
      }
    },
  },
  mounted() {
    this.updateUploadHeaders();

    // 使用安全的事件监听器注册
    this.$safeOn("openCommunityResidentEdit", (data) => {
      console.log('打开住户编辑对话框');
      this.resetForm();
      if (data && data.id) {
        this.residentModel = { ...data };
        // 处理标签数据：将字符串转换为数组
        if (data.tags) {
          this.residentTagsArray = data.tags.split(',').filter(tag => tag.trim());
        } else {
          this.residentTagsArray = [];
        }
        this.dialog.title = "编辑住户信息";
        // 编辑时设置为已验证状态
        this.verificationStatus = 'verified';
        this.verificationMessage = '已验证用户';
        this.lastVerifiedDataHash = this.verificationDataHash; // 记录编辑时的数据哈希
      } else {
        this.dialog.title = "新增住户信息";
        // 新增时重置验证状态
        this.verificationStatus = 'pending';
        this.verificationMessage = '';
        this.lastVerifiedDataHash = '';
        this.residentTagsArray = [];
      }
      this.dialog.show = true;
    });
  },

  beforeUnmount() {
    // 清理验证定时器
    if (this.verifyTimer) {
      clearTimeout(this.verifyTimer);
    }
  },
};
</script>

<style scoped>
.resident-edit-dialog >>> .el-dialog__body {
  padding-top: 10px;
  padding-bottom: 0;
  position: relative;
  border: 1px solid red;
  width: 700px;
  height: 700px;
}
.resident-edit-form {
  padding: 0 10px;
}
.dialog-footer {
  padding: 10px 24px 18px 0;
  text-align: right;
}

.step-content {
  width: 200px;
  display: flex;
  top: 10px;
  left: 200px;
  align-items: center;
  padding-left: 50px;
  position: absolute;
}

.step-left .el-step__title {
  padding-left: -50px; /* 调整这个值来移动文字 */
}
</style>