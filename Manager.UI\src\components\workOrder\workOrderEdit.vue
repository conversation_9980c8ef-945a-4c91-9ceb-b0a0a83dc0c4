<template>
  <el-dialog
    v-model="dialog.show"
    :title="dialog.title"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      :model="workOrderModel"
      :rules="rules"
      ref="workOrderForm"
      label-width="120px"
      class="work-order-edit-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="工单类型" prop="type">
            <el-select v-model="workOrderModel.type" placeholder="请选择工单类型" style="width: 100%;">
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工单状态" prop="status">
            <el-select v-model="workOrderModel.status" placeholder="请选择工单状态" style="width: 100%;">
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">

        <el-col :span="12">
          <el-form-item label="区域类型" prop="regionType">
            <el-select
              v-model="workOrderModel.regionType"
              placeholder="请选择区域类型"
              style="width: 100%;"
              @change="onRegionTypeChange"
            >
              <el-option
                v-for="item in regionTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12" v-if="workOrderModel.regionType === 'house'">
          <el-form-item label="选择住户" prop="residentId">
            <div class="resident-selector-container">
              <el-input
                v-model="residentDisplayText"
                placeholder="请选择住户"
                readonly
                class="resident-input"
              />
              <el-button type="primary" @click="openResidentSelector" class="select-btn">
                选择
              </el-button>
            </div>
          </el-form-item>
        </el-col>

        <!-- <el-col :span="12">
          <el-form-item label="处理人ID" prop="personId">
            <el-input-number
              v-model="workOrderModel.personId"
              placeholder="处理人ID"
              style="width: 100%;"
              :min="0"
            />
          </el-form-item>
        </el-col> -->

        <el-col :span="12">
          <el-form-item label="区域信息" prop="region">
            <!-- 房屋类型：显示住户房产选择 -->
            <div v-if="workOrderModel.regionType === 'house'" class="property-selector-container">
              <el-input
                v-model="propertyDisplayText"
                placeholder="请先选择住户，然后选择房产"
                readonly
                class="property-input"
              />
              <el-button
                type="primary"
                @click="openPropertySelector"
                :disabled="!workOrderModel.residentId"
                class="select-btn"
              >
                选择房产
              </el-button>
            </div>
            <!-- 公共区域类型：手动输入 -->
            <el-input
              v-else-if="workOrderModel.regionType === 'public_area'"
              v-model="workOrderModel.region"
              placeholder="请输入公共区域信息，如：小区大门、停车场、花园等"
            />
            <!-- 未选择区域类型 -->
            <el-input
              v-else
              v-model="workOrderModel.region"
              placeholder="请先选择区域类型"
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>





      <el-form-item label="用户描述" prop="userDescribe">
        <el-input
          v-model="workOrderModel.userDescribe"
          type="textarea"
          :rows="3"
          placeholder="请输入用户描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="处理描述" prop="personDescribe">
        <el-input
          v-model="workOrderModel.personDescribe"
          type="textarea"
          :rows="3"
          placeholder="请输入处理描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="媒体文件" prop="media">
        <el-upload
          class="upload-demo"
          :action="uploadUrl"
          :headers="uploadHeaders"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :before-upload="beforeUpload"
          :file-list="fileList"
          :on-remove="handleRemove"
          multiple
          accept="image/*,video/*"
          list-type="picture-card"
        >
          <el-icon><Plus /></el-icon>
          <template #tip>
            <div class="el-upload__tip">
              支持图片和视频文件，单个文件最大50MB，总数量不限制
            </div>
          </template>
        </el-upload>
      </el-form-item>


    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="save" :loading="saving">保存</el-button>
    </template>
  </el-dialog>

  <!-- 住户选择弹窗 -->
  <el-dialog
    v-model="residentSelectorDialog.show"
    title="选择住户"
    width="800px"
    :close-on-click-modal="false"
    append-to-body
  >
    <div class="resident-selector">
      <div class="selector-header">
        <span class="selected-count">已选择 {{ selectedResident ? 1 : 0 }} 个住户</span>
        <el-button @click="clearResidentSelection" size="small">清空选择</el-button>
      </div>

      <!-- 搜索框 -->
      <div class="search-container" style="margin-bottom: 16px;">
        <el-input
          v-model="residentSearchName"
          placeholder="请输入住户姓名搜索"
          clearable
          @input="onResidentSearch"
          style="width: 300px;">
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 住户列表 -->
      <div class="resident-list-container" v-loading="residentLoading">
        <el-table
          ref="residentTableRef"
          :data="filteredResidentList"
          @current-change="onResidentSelectionChange"
          row-key="residentId"
          max-height="400px"
          highlight-current-row
          style="width: 100%;">
          <el-table-column prop="residentName" label="姓名" width="120" />
          <el-table-column prop="phone" label="电话" width="140" />
          <el-table-column prop="idCardNumber" label="身份证号" show-overflow-tooltip />
          <el-table-column prop="address" label="地址" show-overflow-tooltip />
        </el-table>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="residentSelectorDialog.show = false">取消</el-button>
        <el-button type="primary" @click="confirmResidentSelection">确定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 住户房产选择弹窗 -->
  <el-dialog
    v-model="propertySelectorDialog.show"
    title="选择住户房产"
    width="700px"
    :close-on-click-modal="false"
    append-to-body
  >
    <div class="property-selector">
      <div class="selector-header">
        <span class="selected-count">已选择 {{ selectedProperty ? 1 : 0 }} 个房产</span>
        <el-button @click="clearPropertySelection" size="small">清空选择</el-button>
      </div>

      <!-- 房产列表 -->
      <div class="property-list-container" v-loading="propertyLoading">
        <el-table
          ref="propertyTableRef"
          :data="propertyList"
          @current-change="onPropertySelectionChange"
          row-key="id"
          max-height="400px"
          highlight-current-row
          style="width: 100%;">
          <el-table-column prop="buildingNumber" label="楼栋号" width="100" />
          <el-table-column prop="unitNumber" label="单元号" width="100" />
          <el-table-column prop="roomNumber" label="房间号" width="100" />
          <el-table-column prop="address" label="完整地址" show-overflow-tooltip />
          <el-table-column prop="residentType" label="住户类型" width="100">
            <template #default="scope">
              <el-tag :type="getResidentTypeTag(scope.row.residentType)">
                {{ getResidentTypeLabel(scope.row.residentType) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="propertySelectorDialog.show = false">取消</el-button>
        <el-button type="primary" @click="confirmPropertySelection">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { addWorkOrder, editWorkOrder, WORK_ORDER_TYPES, REGION_TYPES, WORK_ORDER_STATUS } from '@/api/workOrder'
import { listDictByNameEn } from '@/api/system/dict'
import { listCommunityResident } from '@/api/community/communityResident'
import { listResidentProperty } from '@/api/community/communityResidentProperty'
import { getCommunity } from '@/api/community/community'
import { getSelectedCommunityId } from '@/store/modules/options'
import { Search, Plus } from '@element-plus/icons-vue'
import mitt from '@/utils/mitt'

export default {
  name: 'WorkOrderEdit',
  components: {
    Search,
    Plus
  },
  data() {
    return {
      workOrderModel: {
        id: null,
        communityId: null,
        type: '',
        userDescribe: '',
        personDescribe: '',
        media: '',
        status: 'wait_process',
        residentId: null,
        personId: null,
        regionType: '',
        region: ''
      },
      dialog: {
        show: false,
        title: ''
      },
      saving: false,
      fileList: [],
      uploadUrl: import.meta.env.VITE_BASE_API + '/common-api/v1/file/upload',
      imgServer: import.meta.env.VITE_BASE_API + '/common-api/v1/file/',
      uploadHeaders: {
        Authorization: ''
      },
      // 字典选项
      typeOptions: [],
      statusOptions: [],
      regionTypeOptions: [],
      // 住户选择相关
      residentSelectorDialog: {
        show: false
      },
      residentList: [],
      filteredResidentList: [],
      selectedResident: null,
      residentDisplayText: '',
      residentSearchName: '',
      residentLoading: false,
      // 房产选择相关
      propertySelectorDialog: {
        show: false
      },
      propertyList: [],
      selectedProperty: null,
      propertyDisplayText: '',
      propertyLoading: false,
      // 表单验证规则
      rules: {
        type: [
          { required: true, message: '请选择工单类型', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择工单状态', trigger: 'change' }
        ],
        userDescribe: [
          { required: true, message: '请输入用户描述', trigger: 'blur' },
          { max: 500, message: '用户描述不能超过500个字符', trigger: 'blur' }
        ],
        regionType: [
          { required: true, message: '请选择区域类型', trigger: 'change' }
        ],
        region: [
          { required: true, message: '请输入区域信息', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    /**
     * 保存工单
     */
    async save() {
      try {
        await this.$refs.workOrderForm.validate()

        this.saving = true
        const communityId = getSelectedCommunityId()
        if (!communityId) {
          this.$message.error('请先选择小区')
          return
        }

        const data = {
          ...this.workOrderModel,
          communityId: communityId
        }

        if (data.id) {
          await editWorkOrder(data)
          this.$message.success('编辑成功')
        } else {
          delete data.id
          await addWorkOrder(data)
          this.$message.success('添加成功')
        }

        this.handleClose()
        this.$emit('search')
      } catch (error) {
        console.error('保存工单失败:', error)
        if (error.data?.errorMessage) {
          this.$message.error(error.data.errorMessage)
        }
      } finally {
        this.saving = false
      }
    },

    /**
     * 关闭弹窗
     */
    handleClose() {
      this.dialog.show = false
      this.resetForm()
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.workOrderModel = {
        id: null,
        communityId: null,
        type: '',
        userDescribe: '',
        personDescribe: '',
        media: '',
        status: 'wait_process',
        residentId: null,
        personId: null,
        regionType: '',
        region: ''
      }
      this.fileList = []
      this.selectedResident = null
      this.residentDisplayText = ''
      this.residentSearchName = ''
      this.selectedProperty = null
      this.propertyDisplayText = ''
      this.$nextTick(() => {
        this.$refs.workOrderForm?.clearValidate()
      })
    },

    /**
     * 文件上传成功
     */
    handleUploadSuccess(response, _file, fileList) {
      if (response.code === 0) {
        // 收集所有文件路径，包括新上传的和已存在的
        const filePaths = fileList.map(item => {
          // 新上传的文件从response.data获取路径
          if (item.response?.data) {
            return item.response.data
          }
          // 已存在的文件从url中提取路径（去掉imgServer前缀）
          if (item.url && item.url.startsWith(this.imgServer)) {
            return item.url.replace(this.imgServer, '')
          }
          // 如果url就是路径本身
          return item.url
        }).filter(Boolean)

        // 存储为逗号分隔的路径字符串
        this.workOrderModel.media = filePaths.join(',')
        this.$message.success('文件上传成功')
      } else {
        this.$message.error(response.errorMessage || '上传失败')
      }
    },

    /**
     * 文件上传失败
     */
    handleUploadError() {
      this.$message.error('文件上传失败')
    },

    /**
     * 上传前验证
     */
    beforeUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isVideo = file.type.startsWith('video/')
      const isValidType = isImage || isVideo
      const isLt50M = file.size / 1024 / 1024 < 50

      if (!isValidType) {
        this.$message.error('只能上传图片或视频文件!')
        return false
      }
      if (!isLt50M) {
        this.$message.error('上传文件大小不能超过 50MB!')
        return false
      }
      return true
    },

    /**
     * 移除文件
     */
    handleRemove(_file, fileList) {
      // 收集剩余文件的路径
      const filePaths = fileList.map(item => {
        // 新上传的文件从response.data获取路径
        if (item.response?.data) {
          return item.response.data
        }
        // 已存在的文件从url中提取路径（去掉imgServer前缀）
        if (item.url && item.url.startsWith(this.imgServer)) {
          return item.url.replace(this.imgServer, '')
        }
        // 如果url就是路径本身
        return item.url
      }).filter(Boolean)

      // 更新media字段
      this.workOrderModel.media = filePaths.join(',')
    },

    /**
     * 初始化字典数据
     */
    async initDictData() {
      try {
        // 加载工单类型字典
        const typeRes = await listDictByNameEn('work_order_type')
        this.typeOptions = (typeRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))

        // 加载工单状态字典
        const statusRes = await listDictByNameEn('work_order_status')
        this.statusOptions = (statusRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))

        // 加载区域类型字典
        const regionRes = await listDictByNameEn('region_type')
        this.regionTypeOptions = (regionRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))

        // 如果字典数据为空，使用本地常量
        if (this.typeOptions.length === 0) {
          this.typeOptions = WORK_ORDER_TYPES
        }
        if (this.statusOptions.length === 0) {
          this.statusOptions = WORK_ORDER_STATUS
        }
        if (this.regionTypeOptions.length === 0) {
          this.regionTypeOptions = REGION_TYPES
        }
      } catch (err) {
        console.error('加载字典数据失败:', err)
        // 使用本地常量作为备选
        this.typeOptions = WORK_ORDER_TYPES
        this.statusOptions = WORK_ORDER_STATUS
        this.regionTypeOptions = REGION_TYPES
      }
    },

    /**
     * 打开住户选择器
     */
    openResidentSelector() {
      this.residentSelectorDialog.show = true
      this.loadResidentList()
    },

    /**
     * 加载住户列表
     */
    async loadResidentList() {
      const communityId = getSelectedCommunityId()
      if (!communityId) {
        this.$message.warning('请先选择小区')
        return
      }

      this.residentLoading = true
      try {
        const response = await listCommunityResident({
          pageNum: 1,
          pageSize: 500,
          communityId: communityId,
          residentName: this.residentSearchName || undefined
        })

        this.residentList = response.data.data.list || []
        this.filteredResidentList = [...this.residentList]

        // 如果有已选择的住户，设置当前行
        if (this.selectedResident) {
          setTimeout(() => {
            if (this.$refs.residentTableRef) {
              this.$refs.residentTableRef.setCurrentRow(this.selectedResident)
            }
          }, 100)
        }
      } catch (err) {
        this.$message.error(err.data?.errorMessage || '加载住户列表失败')
        this.residentList = []
        this.filteredResidentList = []
      } finally {
        this.residentLoading = false
      }
    },

    /**
     * 住户搜索
     */
    onResidentSearch() {
      if (!this.residentSearchName.trim()) {
        this.filteredResidentList = [...this.residentList]
      } else {
        this.filteredResidentList = this.residentList.filter(resident =>
          resident.residentName.includes(this.residentSearchName.trim())
        )
      }
    },

    /**
     * 住户选择变化事件
     */
    onResidentSelectionChange(currentRow) {
      this.selectedResident = currentRow
    },

    /**
     * 清空住户选择
     */
    clearResidentSelection() {
      this.selectedResident = null
      this.residentSearchName = ''
      if (this.$refs.residentTableRef) {
        this.$refs.residentTableRef.setCurrentRow()
      }
    },

    /**
     * 确认住户选择
     */
    confirmResidentSelection() {
      if (this.selectedResident) {
        this.workOrderModel.residentId = this.selectedResident.residentId
        this.residentDisplayText = this.selectedResident.residentName
        this.residentSelectorDialog.show = false
        this.$message.success('住户选择成功')
      } else {
        this.$message.warning('请选择一个住户')
      }
    },

    /**
     * 处理住户回显
     */
    async handleResidentDisplay(residentId) {
      try {
        const communityId = getSelectedCommunityId()
        if (!communityId) return

        const response = await listCommunityResident({
          pageNum: 1,
          pageSize: 500,
          communityId: communityId
        })

        const residents = response.data.data.list || []
        const resident = residents.find(r => r.residentId === residentId)

        if (resident) {
          this.selectedResident = resident
          this.residentDisplayText = resident.residentName
        } else {
          this.residentDisplayText = `住户ID: ${residentId}`
        }
      } catch (error) {
        console.error('处理住户回显失败:', error)
        this.residentDisplayText = `住户ID: ${residentId}`
      }
    },

    /**
     * 区域类型变化处理
     */
    onRegionTypeChange() {
      // 清空区域信息
      this.workOrderModel.region = ''
      this.selectedProperty = null
      this.propertyDisplayText = ''

      console.log(this.workOrderModel.regionType)
      if(this.workOrderModel.regionType=='house')
      {
        this.workOrderModel.personId=null
      }
    },

    /**
     * 打开房产选择器
     */
    openPropertySelector() {
      if (!this.workOrderModel.residentId) {
        this.$message.warning('请先选择住户')
        return
      }
      this.propertySelectorDialog.show = true
      this.loadPropertyList()
    },

    /**
     * 加载住户房产列表
     */
    async loadPropertyList() {
      if (!this.workOrderModel.residentId) return

      this.propertyLoading = true
      try {
        const communityId = getSelectedCommunityId()
        const response = await listResidentProperty({
          pageNum: 1,
          pageSize: 100,
          residentId: this.workOrderModel.residentId,
          communityId: communityId
        })

        this.propertyList = response.data.data.list || []

        // 如果有已选择的房产，设置当前行
        if (this.selectedProperty) {
          setTimeout(() => {
            if (this.$refs.propertyTableRef) {
              this.$refs.propertyTableRef.setCurrentRow(this.selectedProperty)
            }
          }, 100)
        }
      } catch (err) {
        this.$message.error(err.data?.errorMessage || '加载房产列表失败')
        this.propertyList = []
      } finally {
        this.propertyLoading = false
      }
    },

    /**
     * 房产选择变化事件
     */
    onPropertySelectionChange(currentRow) {
      this.selectedProperty = currentRow
    },

    /**
     * 清空房产选择
     */
    clearPropertySelection() {
      this.selectedProperty = null
      if (this.$refs.propertyTableRef) {
        this.$refs.propertyTableRef.setCurrentRow()
      }
    },

    /**
     * 确认房产选择
     */
    async confirmPropertySelection() {
      if (this.selectedProperty) {
        try {
          // 根据房产信息拼接region字段
          const property = this.selectedProperty
          let regionText = ''

          // 获取小区名称
          const communityId = getSelectedCommunityId()
          let communityName = '小区'

          if (communityId) {
            try {
              const communityRes = await getCommunity(communityId)
              communityName = communityRes.data.data.communityName || '小区'
            } catch (error) {
              console.error('获取小区信息失败:', error)
            }
          }

          // 拼接格式：communityName-buildingNumber-unitNumber(如果有值)-roomNumber
          regionText = communityName + '-' + property.buildingNumber

          if (property.unitNumber) {
            regionText += '-' + property.unitNumber
          }

          regionText += '-' + property.roomNumber

          this.workOrderModel.region = regionText
          this.propertyDisplayText = regionText
          this.propertySelectorDialog.show = false
          this.$message.success('房产选择成功')
        } catch (error) {
          console.error('确认房产选择失败:', error)
          this.$message.error('房产选择失败')
        }
      } else {
        this.$message.warning('请选择一个房产')
      }
    },

    /**
     * 获取住户类型标签样式
     */
    getResidentTypeTag(type) {
      const tagMap = {
        'owner': 'success',
        'tenant': 'warning',
        'family': 'info'
      }
      return tagMap[type] || ''
    },

    /**
     * 获取住户类型标签文本
     */
    getResidentTypeLabel(type) {
      const typeMap = {
        'owner': '业主',
        'tenant': '租户',
        'family': '家属'
      }
      return typeMap[type] || type
    }
  },

  async mounted() {
    await this.initDictData()

    // 监听添加事件
    mitt.on('openWorkOrderAdd', () => {
      this.resetForm()
      this.dialog.show = true
      this.dialog.title = '添加工单'
    })

    // 监听编辑事件
    mitt.on('openWorkOrderEdit', async (workOrder) => {
      this.resetForm()
      this.workOrderModel = { ...workOrder }

      // 处理媒体文件显示
      if (workOrder.media) {
        this.fileList = workOrder.media.split(',').map((filePath, index) => {
          const trimmedPath = filePath.trim()
          if (!trimmedPath) return null

          // 构建完整的文件访问URL
          const fullUrl = this.imgServer + trimmedPath

          // 根据文件扩展名判断文件类型
          const isImage = /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(trimmedPath)
          const isVideo = /\.(mp4|avi|mov|wmv|flv|webm)$/i.test(trimmedPath)

          let fileName = `文件${index + 1}`
          if (isImage) {
            fileName = `图片${index + 1}`
          } else if (isVideo) {
            fileName = `视频${index + 1}`
          }

          return {
            name: fileName,
            url: fullUrl,
            uid: Date.now() + index // 添加唯一标识
          }
        }).filter(Boolean)
      }

      // 处理住户回显
      if (workOrder.residentId) {
        await this.handleResidentDisplay(workOrder.residentId)
      }

      // 处理房产回显
      if (workOrder.regionType === 'house' && workOrder.region) {
        this.propertyDisplayText = workOrder.region
      }

      this.dialog.show = true
      this.dialog.title = '编辑工单'
    })

    // 设置上传请求头
    const token = localStorage.getItem('token')
    if (token) {
      try {
        const tokenObj = JSON.parse(token)
        this.uploadHeaders.Authorization = tokenObj.access_token || token
      } catch (e) {
        this.uploadHeaders.Authorization = token
      }
    }
  },

  beforeUnmount() {
    mitt.off('openWorkOrderAdd')
    mitt.off('openWorkOrderEdit')
  }
}
</script>

<style scoped>
.work-order-edit-form :deep(.el-form-item) {
  margin-bottom: 16px !important;
}

.upload-demo {
  width: 100%;
}

.upload-demo :deep(.el-upload-list) {
  margin-top: 10px;
}

/* 住户选择器样式 */
.resident-selector-container {
  display: flex;
  gap: 8px;
}

.resident-input {
  flex: 1;
}

.select-btn {
  flex-shrink: 0;
}

.resident-selector {
  padding: 16px 0;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.selected-count {
  font-weight: 500;
  color: #409eff;
}

.search-container {
  margin-bottom: 16px;
}

.resident-list-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

/* 房产选择器样式 */
.property-selector-container {
  display: flex;
  gap: 8px;
}

.property-input {
  flex: 1;
}

.property-selector {
  padding: 16px 0;
}

.property-list-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .resident-selector-container {
    flex-direction: column;
  }

  .select-btn {
    width: 100%;
  }
}
</style>
