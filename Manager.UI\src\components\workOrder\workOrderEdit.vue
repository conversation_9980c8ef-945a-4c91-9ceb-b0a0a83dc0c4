<template>
  <el-dialog
    v-model="dialog.show"
    :title="dialog.title"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      :model="workOrderModel"
      :rules="rules"
      ref="workOrderForm"
      label-width="120px"
      class="work-order-edit-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="工单类型" prop="type">
            <el-select v-model="workOrderModel.type" placeholder="请选择工单类型" style="width: 100%;">
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工单状态" prop="status">
            <el-select v-model="workOrderModel.status" placeholder="请选择工单状态" style="width: 100%;">
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="区域类型" prop="regionType">
            <el-select v-model="workOrderModel.regionType" placeholder="请选择区域类型" style="width: 100%;">
              <el-option
                v-for="item in regionTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="区域信息" prop="region">
            <el-input v-model="workOrderModel.region" placeholder="请输入区域信息" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="用户描述" prop="userDescribe">
        <el-input
          v-model="workOrderModel.userDescribe"
          type="textarea"
          :rows="3"
          placeholder="请输入用户描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="处理描述" prop="personDescribe">
        <el-input
          v-model="workOrderModel.personDescribe"
          type="textarea"
          :rows="3"
          placeholder="请输入处理描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="媒体文件" prop="media">
        <el-upload
          class="upload-demo"
          :action="uploadUrl"
          :headers="uploadHeaders"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :file-list="fileList"
          :on-remove="handleRemove"
          multiple
          :limit="5"
          accept="image/*,video/*"
        >
          <el-button type="primary">点击上传</el-button>
          <template #tip>
            <div class="el-upload__tip">
              支持图片和视频文件，最多上传5个文件
            </div>
          </template>
        </el-upload>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="住户ID" prop="residentId">
            <el-input-number
              v-model="workOrderModel.residentId"
              placeholder="住户ID"
              style="width: 100%;"
              :min="0"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="处理人ID" prop="personId">
            <el-input-number
              v-model="workOrderModel.personId"
              placeholder="处理人ID"
              style="width: 100%;"
              :min="0"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="save" :loading="saving">保存</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { addWorkOrder, editWorkOrder, WORK_ORDER_TYPES, REGION_TYPES, WORK_ORDER_STATUS } from '@/api/workOrder'
import { listDictByNameEn } from '@/api/system/dict'
import { getSelectedCommunityId } from '@/store/modules/options'
import mitt from '@/utils/mitt'

export default {
  name: 'WorkOrderEdit',
  data() {
    return {
      workOrderModel: {
        id: null,
        communityId: null,
        type: '',
        userDescribe: '',
        personDescribe: '',
        media: '',
        status: 'wait_process',
        residentId: null,
        personId: null,
        regionType: '',
        region: ''
      },
      dialog: {
        show: false,
        title: ''
      },
      saving: false,
      fileList: [],
      uploadUrl: import.meta.env.VITE_BASE_API + '/common-api/v1/file/upload',
      uploadHeaders: {
        Authorization: ''
      },
      // 字典选项
      typeOptions: [],
      statusOptions: [],
      regionTypeOptions: [],
      // 表单验证规则
      rules: {
        type: [
          { required: true, message: '请选择工单类型', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择工单状态', trigger: 'change' }
        ],
        userDescribe: [
          { required: true, message: '请输入用户描述', trigger: 'blur' },
          { max: 500, message: '用户描述不能超过500个字符', trigger: 'blur' }
        ],
        regionType: [
          { required: true, message: '请选择区域类型', trigger: 'change' }
        ],
        region: [
          { required: true, message: '请输入区域信息', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    /**
     * 保存工单
     */
    async save() {
      try {
        await this.$refs.workOrderForm.validate()
        
        this.saving = true
        const communityId = getSelectedCommunityId()
        if (!communityId) {
          this.$message.error('请先选择小区')
          return
        }

        const data = {
          ...this.workOrderModel,
          communityId: communityId
        }

        if (data.id) {
          await editWorkOrder(data)
          this.$message.success('编辑成功')
        } else {
          delete data.id
          await addWorkOrder(data)
          this.$message.success('添加成功')
        }

        this.handleClose()
        this.$emit('search')
      } catch (error) {
        console.error('保存工单失败:', error)
        if (error.data?.errorMessage) {
          this.$message.error(error.data.errorMessage)
        }
      } finally {
        this.saving = false
      }
    },

    /**
     * 关闭弹窗
     */
    handleClose() {
      this.dialog.show = false
      this.resetForm()
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.workOrderModel = {
        id: null,
        communityId: null,
        type: '',
        userDescribe: '',
        personDescribe: '',
        media: '',
        status: 'wait_process',
        residentId: null,
        personId: null,
        regionType: '',
        region: ''
      }
      this.fileList = []
      this.$nextTick(() => {
        this.$refs.workOrderForm?.clearValidate()
      })
    },

    /**
     * 文件上传成功
     */
    handleUploadSuccess(response, file, fileList) {
      if (response.code === 200) {
        const urls = fileList.map(item => {
          return item.response?.data || item.url
        }).filter(Boolean)
        this.workOrderModel.media = urls.join(',')
      } else {
        this.$message.error(response.errorMessage || '上传失败')
      }
    },

    /**
     * 文件上传失败
     */
    handleUploadError(error, file, fileList) {
      this.$message.error('文件上传失败')
    },

    /**
     * 移除文件
     */
    handleRemove(file, fileList) {
      const urls = fileList.map(item => {
        return item.response?.data || item.url
      }).filter(Boolean)
      this.workOrderModel.media = urls.join(',')
    },

    /**
     * 初始化字典数据
     */
    async initDictData() {
      try {
        // 加载工单类型字典
        const typeRes = await listDictByNameEn('work_order_type')
        this.typeOptions = (typeRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))

        // 加载工单状态字典
        const statusRes = await listDictByNameEn('work_order_status')
        this.statusOptions = (statusRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))

        // 加载区域类型字典
        const regionRes = await listDictByNameEn('region_type')
        this.regionTypeOptions = (regionRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))

        // 如果字典数据为空，使用本地常量
        if (this.typeOptions.length === 0) {
          this.typeOptions = WORK_ORDER_TYPES
        }
        if (this.statusOptions.length === 0) {
          this.statusOptions = WORK_ORDER_STATUS
        }
        if (this.regionTypeOptions.length === 0) {
          this.regionTypeOptions = REGION_TYPES
        }
      } catch (err) {
        console.error('加载字典数据失败:', err)
        // 使用本地常量作为备选
        this.typeOptions = WORK_ORDER_TYPES
        this.statusOptions = WORK_ORDER_STATUS
        this.regionTypeOptions = REGION_TYPES
      }
    }
  },

  async mounted() {
    await this.initDictData()

    // 监听添加事件
    mitt.on('openWorkOrderAdd', () => {
      this.resetForm()
      this.dialog.show = true
      this.dialog.title = '添加工单'
    })

    // 监听编辑事件
    mitt.on('openWorkOrderEdit', (workOrder) => {
      this.resetForm()
      this.workOrderModel = { ...workOrder }
      
      // 处理媒体文件显示
      if (workOrder.media) {
        this.fileList = workOrder.media.split(',').map((url, index) => ({
          name: `文件${index + 1}`,
          url: url
        }))
      }
      
      this.dialog.show = true
      this.dialog.title = '编辑工单'
    })

    // 设置上传请求头
    const token = localStorage.getItem('token')
    if (token) {
      this.uploadHeaders.Authorization = `Bearer ${token}`
    }
  },

  beforeUnmount() {
    mitt.off('openWorkOrderAdd')
    mitt.off('openWorkOrderEdit')
  }
}
</script>

<style scoped>
.work-order-edit-form :deep(.el-form-item) {
  margin-bottom: 16px !important;
}

.upload-demo {
  width: 100%;
}

.upload-demo :deep(.el-upload-list) {
  margin-top: 10px;
}
</style>
